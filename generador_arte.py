#!/usr/bin/env python3
"""
Generador de Arte Generativo
Crea patrones artísticos y animaciones abstractas usando algoritmos matemáticos
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.colors import LinearSegmentedColormap
import math
import random
from datetime import datetime

class GeneradorArte:
    def __init__(self):
        self.fig = None
        self.ax = None
        self.colores_personalizados = [
            ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
            ['#6C5CE7', '#A29BFE', '#FD79A8', '#FDCB6E', '#E17055'],
            ['#00B894', '#00CEC9', '#0984E3', '#6C5CE7', '#A29BFE'],
            ['#FF7675', '#74B9FF', '#81ECEC', '#00B894', '#FDCB6E']
        ]
    
    def configurar_lienzo(self, titulo="Arte Generativo"):
        """Configura el lienzo para dibujar"""
        plt.style.use('dark_background')
        self.fig, self.ax = plt.subplots(figsize=(12, 12))
        self.ax.set_xlim(-10, 10)
        self.ax.set_ylim(-10, 10)
        self.ax.set_aspect('equal')
        self.ax.axis('off')
        self.fig.patch.set_facecolor('black')
        plt.title(titulo, color='white', fontsize=16, pad=20)
    
    def espiral_fibonacci(self, n_puntos=2000):
        """Genera una espiral basada en la secuencia de Fibonacci"""
        self.configurar_lienzo("Espiral de Fibonacci")
        
        # Ángulo dorado
        phi = (1 + math.sqrt(5)) / 2
        angulo_dorado = 2 * math.pi / (phi ** 2)
        
        colores = random.choice(self.colores_personalizados)
        
        for i in range(n_puntos):
            # Radio crece con la raíz cuadrada del índice
            r = 0.1 * math.sqrt(i)
            theta = i * angulo_dorado
            
            x = r * math.cos(theta)
            y = r * math.sin(theta)
            
            # Color que cambia gradualmente
            color_idx = int((i / n_puntos) * (len(colores) - 1))
            color = colores[color_idx]
            
            # Tamaño del punto varía
            size = max(1, 50 - i * 0.02)
            alpha = max(0.1, 1 - i * 0.0005)
            
            self.ax.scatter(x, y, c=color, s=size, alpha=alpha)
        
        plt.tight_layout()
        plt.show()
    
    def mandala_geometrico(self, n_capas=8, n_puntos=12):
        """Crea un mandala geométrico con patrones radiales"""
        self.configurar_lienzo("Mandala Geométrico")
        
        colores = random.choice(self.colores_personalizados)
        
        for capa in range(1, n_capas + 1):
            radio = capa * 1.2
            
            for i in range(n_puntos * capa):
                angulo = (2 * math.pi * i) / (n_puntos * capa)
                
                # Variaciones en el radio para crear patrones
                variacion = 0.3 * math.sin(6 * angulo) * math.cos(4 * angulo)
                r = radio + variacion
                
                x = r * math.cos(angulo)
                y = r * math.sin(angulo)
                
                # Color basado en la capa y posición
                color_idx = (capa - 1) % len(colores)
                color = colores[color_idx]
                
                # Conectar puntos para formar patrones
                if i > 0:
                    x_prev = r_prev * math.cos(angulo_prev)
                    y_prev = r_prev * math.sin(angulo_prev)
                    self.ax.plot([x_prev, x], [y_prev, y], 
                               color=color, alpha=0.6, linewidth=0.8)
                
                # Dibujar punto
                self.ax.scatter(x, y, c=color, s=20, alpha=0.8)
                
                r_prev, angulo_prev = r, angulo
        
        plt.tight_layout()
        plt.show()
    
    def ondas_interferencia(self, n_ondas=5):
        """Crea patrones de interferencia de ondas"""
        self.configurar_lienzo("Interferencia de Ondas")
        
        # Crear una malla de puntos
        x = np.linspace(-10, 10, 400)
        y = np.linspace(-10, 10, 400)
        X, Y = np.meshgrid(x, y)
        
        # Inicializar la función de onda
        Z = np.zeros_like(X)
        
        # Generar múltiples fuentes de ondas
        for i in range(n_ondas):
            # Posición aleatoria de la fuente
            cx = random.uniform(-5, 5)
            cy = random.uniform(-5, 5)
            
            # Frecuencia y fase aleatorias
            freq = random.uniform(0.5, 2.0)
            fase = random.uniform(0, 2 * math.pi)
            
            # Calcular distancia desde cada punto a la fuente
            distancia = np.sqrt((X - cx)**2 + (Y - cy)**2)
            
            # Agregar onda circular
            Z += np.sin(freq * distancia + fase) * np.exp(-distancia * 0.1)
        
        # Crear mapa de colores personalizado
        colores = random.choice(self.colores_personalizados)
        cmap = LinearSegmentedColormap.from_list("custom", colores)
        
        # Dibujar el patrón
        im = self.ax.contourf(X, Y, Z, levels=50, cmap=cmap, alpha=0.8)
        self.ax.contour(X, Y, Z, levels=20, colors='white', alpha=0.3, linewidths=0.5)
        
        plt.tight_layout()
        plt.show()
    
    def fractal_arbol(self, x=0, y=-8, angulo=90, longitud=3, profundidad=10):
        """Genera un árbol fractal"""
        if profundidad == 0:
            return
        
        # Calcular punto final de la rama
        x_fin = x + longitud * math.cos(math.radians(angulo))
        y_fin = y + longitud * math.sin(math.radians(angulo))
        
        # Color basado en la profundidad
        colores = random.choice(self.colores_personalizados)
        color_idx = min(profundidad - 1, len(colores) - 1)
        color = colores[color_idx]
        
        # Dibujar la rama
        self.ax.plot([x, x_fin], [y, y_fin], 
                    color=color, linewidth=profundidad * 0.5, alpha=0.8)
        
        # Recursión para las ramas
        if profundidad > 1:
            # Rama izquierda
            self.fractal_arbol(x_fin, y_fin, angulo + 25, 
                             longitud * 0.7, profundidad - 1)
            # Rama derecha
            self.fractal_arbol(x_fin, y_fin, angulo - 25, 
                             longitud * 0.7, profundidad - 1)
    
    def generar_arbol_fractal(self):
        """Wrapper para generar el árbol fractal"""
        self.configurar_lienzo("Árbol Fractal")
        self.fractal_arbol()
        plt.tight_layout()
        plt.show()
    
    def animacion_espiral(self, frames=200):
        """Crea una animación de espiral evolutiva"""
        self.configurar_lienzo("Espiral Animada")
        
        def animar(frame):
            self.ax.clear()
            self.ax.set_xlim(-10, 10)
            self.ax.set_ylim(-10, 10)
            self.ax.set_aspect('equal')
            self.ax.axis('off')
            self.ax.set_facecolor('black')
            
            colores = self.colores_personalizados[0]
            
            # Parámetros que cambian con el tiempo
            n_puntos = min(1000, frame * 5)
            fase = frame * 0.1
            
            for i in range(n_puntos):
                t = i * 0.1
                r = 0.1 * t
                
                # Espiral con variación temporal
                x = r * math.cos(t + fase) * math.cos(t * 0.1)
                y = r * math.sin(t + fase) * math.sin(t * 0.1)
                
                color_idx = int((i / n_puntos) * (len(colores) - 1))
                color = colores[color_idx]
                
                size = max(1, 30 - i * 0.02)
                alpha = max(0.1, 1 - i * 0.001)
                
                self.ax.scatter(x, y, c=color, s=size, alpha=alpha)
        
        ani = animation.FuncAnimation(self.fig, animar, frames=frames, 
                                    interval=50, repeat=True)
        plt.show()
        return ani
    
    def menu_interactivo(self):
        """Menú interactivo para seleccionar el tipo de arte"""
        print("\n🎨 GENERADOR DE ARTE GENERATIVO 🎨")
        print("=" * 40)
        print("1. Espiral de Fibonacci")
        print("2. Mandala Geométrico")
        print("3. Ondas de Interferencia")
        print("4. Árbol Fractal")
        print("5. Animación Espiral")
        print("6. Generar Todo")
        print("0. Salir")
        print("=" * 40)
        
        while True:
            try:
                opcion = input("\nSelecciona una opción (0-6): ").strip()
                
                if opcion == "0":
                    print("¡Gracias por usar el Generador de Arte! 🎨")
                    break
                elif opcion == "1":
                    self.espiral_fibonacci()
                elif opcion == "2":
                    self.mandala_geometrico()
                elif opcion == "3":
                    self.ondas_interferencia()
                elif opcion == "4":
                    self.generar_arbol_fractal()
                elif opcion == "5":
                    print("Presiona Ctrl+C para detener la animación")
                    self.animacion_espiral()
                elif opcion == "6":
                    print("Generando todas las obras de arte...")
                    self.espiral_fibonacci()
                    self.mandala_geometrico()
                    self.ondas_interferencia()
                    self.generar_arbol_fractal()
                else:
                    print("❌ Opción no válida. Intenta de nuevo.")
                    
            except KeyboardInterrupt:
                print("\n\n🎨 Programa terminado por el usuario.")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

def main():
    """Función principal"""
    generador = GeneradorArte()
    generador.menu_interactivo()

if __name__ == "__main__":
    main()
