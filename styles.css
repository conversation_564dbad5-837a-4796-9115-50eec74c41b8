/* Reset y configuración base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

.header h1 {
    text-align: center;
    color: #4a5568;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.stats {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 15px;
}

.stat-item {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 12px 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-3px);
}

.stat-label {
    font-weight: 600;
    font-size: 0.9rem;
}

.stat-value {
    font-weight: 700;
    font-size: 1.1rem;
    margin-left: 8px;
}

/* Área de juego */
.game-area {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    flex: 1;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

.game-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.categoria, .dificultad {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    padding: 10px 20px;
    border-radius: 12px;
    font-weight: 600;
}

.palabra-container {
    text-align: center;
    margin-bottom: 30px;
}

.palabra-container h2 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.palabra-desordenada {
    font-size: 3rem;
    font-weight: 700;
    color: #e17055;
    background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
    padding: 20px;
    border-radius: 15px;
    margin: 20px 0;
    letter-spacing: 8px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.pista-container {
    margin-top: 20px;
}

.btn-pista {
    background: linear-gradient(135deg, #a29bfe, #6c5ce7);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-pista:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
}

.pista {
    background: linear-gradient(135deg, #81ecec, #00cec9);
    color: #2d3436;
    padding: 15px;
    border-radius: 12px;
    margin-top: 15px;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.hidden {
    display: none;
}

/* Input y botones */
.input-container {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

#respuesta {
    flex: 1;
    padding: 15px 20px;
    border: 3px solid #ddd;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 200px;
}

#respuesta:focus {
    outline: none;
    border-color: #74b9ff;
    box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.2);
}

.btn-verificar {
    background: linear-gradient(135deg, #00b894, #00cec9);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-verificar:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
}

/* Feedback */
.feedback {
    text-align: center;
    padding: 15px;
    border-radius: 12px;
    margin-bottom: 25px;
    font-weight: 600;
    font-size: 1.1rem;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feedback.correcto {
    background: linear-gradient(135deg, #00b894, #00cec9);
    color: white;
}

.feedback.incorrecto {
    background: linear-gradient(135deg, #ff7675, #e84393);
    color: white;
}

/* Progreso */
.progreso-container {
    margin-bottom: 25px;
}

.progreso-label {
    font-weight: 600;
    margin-bottom: 10px;
    color: #4a5568;
}

.progreso-bar {
    background: #e9ecef;
    border-radius: 10px;
    height: 20px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progreso-fill {
    background: linear-gradient(135deg, #00b894, #00cec9);
    height: 100%;
    transition: width 0.5s ease;
    border-radius: 10px;
}

.progreso-texto {
    text-align: center;
    margin-top: 8px;
    font-weight: 600;
    color: #4a5568;
}

/* Botones de control */
.botones-control {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-saltar, .btn-nuevo-juego {
    padding: 12px 25px;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-saltar {
    background: linear-gradient(135deg, #fdcb6e, #e17055);
    color: white;
}

.btn-nuevo-juego {
    background: linear-gradient(135deg, #a29bfe, #6c5ce7);
    color: white;
}

.btn-saltar:hover, .btn-nuevo-juego:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
}

/* Modales */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-content h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 2rem;
}

.modal-content p {
    margin-bottom: 15px;
    font-size: 1.1rem;
    color: #666;
}

.btn-siguiente-nivel, .btn-reiniciar {
    background: linear-gradient(135deg, #00b894, #00cec9);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 700;
    font-size: 1.1rem;
    margin-top: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-siguiente-nivel:hover, .btn-reiniciar:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
}

/* Footer */
.footer {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .palabra-desordenada {
        font-size: 2rem;
        letter-spacing: 4px;
    }
    
    .stats {
        justify-content: center;
    }
    
    .game-info {
        justify-content: center;
    }
    
    .input-container {
        flex-direction: column;
    }
    
    .botones-control {
        flex-direction: column;
        align-items: center;
    }
}
