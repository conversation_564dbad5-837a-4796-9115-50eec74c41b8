# 🎨 Proyectos de Entretenimiento

Este repositorio contiene dos proyectos interactivos desarrollados en Python y JavaScript:

## 1. 🎨 Generador de Arte Generativo (Python)

Un programa que crea patrones artísticos y animaciones abstractas usando algoritmos matemáticos.

### Características:
- **Espiral de Fibonacci**: Genera espirales basadas en la secuencia de Fibonacci
- **Mandala Geométrico**: Crea mandalas con patrones radiales complejos
- **Ondas de Interferencia**: Simula patrones de interferencia de múltiples ondas
- **Árbol Fractal**: Genera árboles fractales recursivos
- **Animación Espiral**: Crea animaciones dinámicas de espirales evolutivas
- **Colores Personalizados**: Paletas de colores cuidadosamente seleccionadas
- **Interfaz Interactiva**: Menú fácil de usar para seleccionar diferentes tipos de arte

### Instalación y Uso:

1. **Instalar dependencias:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Ejecutar el programa:**
   ```bash
   python generador_arte.py
   ```

3. **Usar el menú interactivo:**
   - Selecciona el número correspondiente al tipo de arte que quieres generar
   - Presiona Enter para confirmar
   - Para las animaciones, presiona Ctrl+C para detenerlas
   - Selecciona opción 6 para generar todos los tipos de arte
   - Selecciona opción 0 para salir

### Ejemplos de Arte Generado:
- **Espiral de Fibonacci**: Patrones naturales basados en la proporción áurea
- **Mandala**: Diseños simétricos con múltiples capas y colores
- **Interferencia**: Patrones ondulatorios complejos y coloridos
- **Fractal**: Estructuras auto-similares que se repiten a diferentes escalas

---

## 2. 🧩 Juego de Adivinanza Avanzado (JavaScript/HTML/CSS)

Un juego web interactivo donde debes adivinar palabras desordenadas con sistema de puntuación y niveles.

### Características:
- **5 Niveles de Dificultad**: Desde animales fáciles hasta conceptos científicos complejos
- **Sistema de Puntuación**: Gana puntos por respuestas correctas, pierde por pistas y saltos
- **Sistema de Vidas**: 3 vidas por juego, recupera una vida al pasar de nivel
- **Categorías Temáticas**: Animales, Frutas, Países, Profesiones, Ciencia
- **Pistas Inteligentes**: Obtén ayuda a cambio de puntos
- **Efectos Visuales**: Animaciones y partículas para una experiencia inmersiva
- **Diseño Responsivo**: Funciona perfectamente en móviles y escritorio
- **Progreso Visual**: Barra de progreso para cada nivel

### Cómo Jugar:

1. **Abrir el juego:**
   - Abre `index.html` en tu navegador web
   - No requiere instalación adicional

2. **Mecánicas del juego:**
   - Se te presenta una palabra desordenada
   - Escribe la palabra correcta en el campo de texto
   - Presiona "Verificar" o Enter para enviar tu respuesta
   - Usa pistas si necesitas ayuda (-10 puntos)
   - Salta palabras difíciles (-5 puntos)
   - Completa 5 palabras para pasar al siguiente nivel

3. **Sistema de Puntuación:**
   - **Respuesta correcta**: Nivel × 10 + longitud de palabra + bonus (sin pista: +5)
   - **Usar pista**: -10 puntos
   - **Saltar palabra**: -5 puntos
   - **Bonus de nivel**: Nivel × 20 puntos al completar

4. **Niveles:**
   - **Nivel 1**: Animales (Fácil)
   - **Nivel 2**: Frutas (Fácil-Medio)
   - **Nivel 3**: Países (Medio)
   - **Nivel 4**: Profesiones (Medio-Difícil)
   - **Nivel 5**: Ciencia (Difícil)

### Controles:
- **Enter**: Verificar respuesta
- **💡 Obtener Pista**: Muestra una pista útil
- **⏭️ Saltar**: Pasa a la siguiente palabra
- **🔄 Nuevo Juego**: Reinicia el juego completo

---

## 🚀 Tecnologías Utilizadas

### Generador de Arte:
- **Python 3.7+**
- **Matplotlib**: Para visualización y gráficos
- **NumPy**: Para cálculos matemáticos eficientes
- **Algoritmos matemáticos**: Fibonacci, fractales, ondas sinusoidales

### Juego de Adivinanza:
- **HTML5**: Estructura semántica
- **CSS3**: Diseño moderno con gradientes y animaciones
- **JavaScript ES6+**: Lógica del juego y efectos interactivos
- **Google Fonts**: Tipografía Poppins
- **Responsive Design**: Compatible con todos los dispositivos

---

## 📁 Estructura del Proyecto

```
proyecto/
├── generador_arte.py      # Generador de arte en Python
├── requirements.txt       # Dependencias de Python
├── index.html            # Página principal del juego
├── styles.css            # Estilos del juego
├── script.js             # Lógica del juego en JavaScript
└── README.md             # Este archivo
```

---

## 🎯 Características Destacadas

### Generador de Arte:
- ✅ Algoritmos matemáticos avanzados
- ✅ Múltiples tipos de patrones artísticos
- ✅ Animaciones fluidas y dinámicas
- ✅ Paletas de colores profesionales
- ✅ Interfaz de usuario intuitiva
- ✅ Código modular y extensible

### Juego de Adivinanza:
- ✅ Progresión de dificultad inteligente
- ✅ Sistema de puntuación balanceado
- ✅ Efectos visuales atractivos
- ✅ Diseño responsive y moderno
- ✅ Múltiples categorías temáticas
- ✅ Experiencia de usuario pulida

---

## 🔧 Personalización

### Generador de Arte:
- Modifica las paletas de colores en `colores_personalizados`
- Ajusta parámetros de los algoritmos (número de puntos, capas, etc.)
- Agrega nuevos tipos de patrones artísticos

### Juego de Adivinanza:
- Añade nuevas categorías y palabras en el objeto `palabras`
- Modifica el sistema de puntuación en `calcularPuntos()`
- Personaliza colores y estilos en `styles.css`
- Agrega nuevos efectos visuales en la clase `EfectosVisuales`

---

## 🎮 ¡Disfruta!

Ambos proyectos están diseñados para ser educativos y entretenidos. El generador de arte te permite explorar la belleza de las matemáticas, mientras que el juego de adivinanza desafía tu vocabulario y agilidad mental.

¡Experimenta, personaliza y diviértete! 🎉
