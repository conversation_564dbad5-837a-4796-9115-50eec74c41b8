// Juego de Adivinanza Avanzado
class JuegoAdivinanza {
    constructor() {
        this.nivel = 1;
        this.puntuacion = 0;
        this.vidas = 3;
        this.palabraActual = '';
        this.palabraDesordenada = '';
        this.pistaActual = '';
        this.categoriaActual = '';
        this.dificultadActual = '';
        this.palabrasCompletadas = 0;
        this.palabrasPorNivel = 5;
        this.pistaUsada = false;
        
        // Base de datos de palabras por categoría y dificultad
        this.palabras = {
            1: { // Nivel 1 - Fácil
                categoria: 'Animales',
                dificultad: 'Fácil',
                palabras: [
                    { palabra: 'GATO', pista: 'Mascota felina que maúlla' },
                    { palabra: 'PERRO', pista: 'Mejor amigo del hombre' },
                    { palabra: 'PATO', pista: 'Ave acuática que hace cuac' },
                    { palabra: 'LEON', pista: 'Rey de la selva' },
                    { palabra: 'OSO', pista: 'Animal grande que hiberna' },
                    { palabra: 'PEZ', pista: 'Animal que vive en el agua' },
                    { palabra: 'RANA', pista: 'Anfibio que salta y croa' }
                ]
            },
            2: { // Nivel 2 - Fácil-Medio
                categoria: 'Frutas',
                dificultad: 'Fácil-Medio',
                palabras: [
                    { palabra: 'MANZANA', pista: 'Fruta roja o verde, muy común' },
                    { palabra: 'PLATANO', pista: 'Fruta amarilla y alargada' },
                    { palabra: 'NARANJA', pista: 'Fruta cítrica del mismo color' },
                    { palabra: 'FRESA', pista: 'Fruta roja con semillas por fuera' },
                    { palabra: 'UVA', pista: 'Fruta pequeña que crece en racimos' },
                    { palabra: 'PERA', pista: 'Fruta con forma de bombilla' },
                    { palabra: 'KIWI', pista: 'Fruta verde por dentro, marrón por fuera' }
                ]
            },
            3: { // Nivel 3 - Medio
                categoria: 'Países',
                dificultad: 'Medio',
                palabras: [
                    { palabra: 'ESPAÑA', pista: 'País europeo famoso por el flamenco' },
                    { palabra: 'FRANCIA', pista: 'País de la Torre Eiffel' },
                    { palabra: 'ITALIA', pista: 'País con forma de bota' },
                    { palabra: 'BRASIL', pista: 'País sudamericano famoso por el fútbol' },
                    { palabra: 'JAPON', pista: 'País asiático del sol naciente' },
                    { palabra: 'CANADA', pista: 'País norteamericano famoso por el jarabe de arce' },
                    { palabra: 'EGIPTO', pista: 'País africano de las pirámides' }
                ]
            },
            4: { // Nivel 4 - Medio-Difícil
                categoria: 'Profesiones',
                dificultad: 'Medio-Difícil',
                palabras: [
                    { palabra: 'MEDICO', pista: 'Profesional que cura enfermedades' },
                    { palabra: 'INGENIERO', pista: 'Profesional que diseña y construye' },
                    { palabra: 'PROFESOR', pista: 'Persona que enseña en escuelas' },
                    { palabra: 'BOMBERO', pista: 'Héroe que apaga incendios' },
                    { palabra: 'PILOTO', pista: 'Persona que vuela aviones' },
                    { palabra: 'CHEF', pista: 'Experto en cocina' },
                    { palabra: 'ARQUITECTO', pista: 'Diseñador de edificios' }
                ]
            },
            5: { // Nivel 5 - Difícil
                categoria: 'Ciencia',
                dificultad: 'Difícil',
                palabras: [
                    { palabra: 'MOLECULA', pista: 'Unidad básica de los compuestos químicos' },
                    { palabra: 'FOTOSINTESIS', pista: 'Proceso por el cual las plantas producen energía' },
                    { palabra: 'CROMOSOMA', pista: 'Estructura que contiene genes' },
                    { palabra: 'NEURONA', pista: 'Célula del sistema nervioso' },
                    { palabra: 'GRAVEDAD', pista: 'Fuerza que nos mantiene en la Tierra' },
                    { palabra: 'ELECTRON', pista: 'Partícula subatómica con carga negativa' },
                    { palabra: 'ECOSISTEMA', pista: 'Comunidad de seres vivos y su ambiente' }
                ]
            }
        };
        
        this.inicializar();
    }
    
    inicializar() {
        this.actualizarInterfaz();
        this.configurarEventos();
        this.nuevaPalabra();
    }
    
    configurarEventos() {
        // Botón verificar
        document.getElementById('btn-verificar').addEventListener('click', () => {
            this.verificarRespuesta();
        });
        
        // Enter en el input
        document.getElementById('respuesta').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.verificarRespuesta();
            }
        });
        
        // Botón pista
        document.getElementById('btn-pista').addEventListener('click', () => {
            this.mostrarPista();
        });
        
        // Botón saltar
        document.getElementById('btn-saltar').addEventListener('click', () => {
            this.saltarPalabra();
        });
        
        // Botón nuevo juego
        document.getElementById('btn-nuevo-juego').addEventListener('click', () => {
            this.nuevoJuego();
        });
        
        // Botones de modales
        document.getElementById('btn-siguiente-nivel').addEventListener('click', () => {
            this.siguienteNivel();
        });
        
        document.getElementById('btn-reiniciar').addEventListener('click', () => {
            this.nuevoJuego();
        });
        
        document.getElementById('btn-jugar-otra-vez').addEventListener('click', () => {
            this.nuevoJuego();
        });
    }
    
    desordenarPalabra(palabra) {
        let letras = palabra.split('');
        
        // Algoritmo Fisher-Yates para mezclar
        for (let i = letras.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [letras[i], letras[j]] = [letras[j], letras[i]];
        }
        
        // Asegurar que la palabra desordenada sea diferente a la original
        let palabraDesordenada = letras.join('');
        let intentos = 0;
        while (palabraDesordenada === palabra && intentos < 10) {
            for (let i = letras.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [letras[i], letras[j]] = [letras[j], letras[i]];
            }
            palabraDesordenada = letras.join('');
            intentos++;
        }
        
        return palabraDesordenada;
    }
    
    nuevaPalabra() {
        const nivelData = this.palabras[this.nivel];
        if (!nivelData) {
            this.mostrarVictoria();
            return;
        }
        
        const palabrasDisponibles = nivelData.palabras;
        const palabraAleatoria = palabrasDisponibles[Math.floor(Math.random() * palabrasDisponibles.length)];
        
        this.palabraActual = palabraAleatoria.palabra;
        this.pistaActual = palabraAleatoria.pista;
        this.categoriaActual = nivelData.categoria;
        this.dificultadActual = nivelData.dificultad;
        this.palabraDesordenada = this.desordenarPalabra(this.palabraActual);
        this.pistaUsada = false;
        
        // Actualizar interfaz
        document.getElementById('palabra-desordenada').textContent = this.palabraDesordenada;
        document.getElementById('categoria').textContent = this.categoriaActual;
        document.getElementById('dificultad').textContent = this.dificultadActual;
        document.getElementById('respuesta').value = '';
        document.getElementById('pista').classList.add('hidden');
        document.getElementById('btn-pista').style.display = 'inline-block';
        
        this.limpiarFeedback();
    }
    
    verificarRespuesta() {
        const respuesta = document.getElementById('respuesta').value.trim().toUpperCase();
        
        if (!respuesta) {
            this.mostrarFeedback('¡Escribe una respuesta!', 'incorrecto');
            return;
        }
        
        if (respuesta === this.palabraActual) {
            this.respuestaCorrecta();
        } else {
            this.respuestaIncorrecta();
        }
    }
    
    respuestaCorrecta() {
        // Calcular puntos
        let puntos = this.calcularPuntos();
        this.puntuacion += puntos;
        this.palabrasCompletadas++;
        
        this.mostrarFeedback(`¡Correcto! +${puntos} puntos`, 'correcto');
        this.actualizarInterfaz();
        
        // Verificar si completó el nivel
        if (this.palabrasCompletadas >= this.palabrasPorNivel) {
            setTimeout(() => {
                this.completarNivel();
            }, 1500);
        } else {
            setTimeout(() => {
                this.nuevaPalabra();
            }, 1500);
        }
    }
    
    respuestaIncorrecta() {
        this.vidas--;
        this.mostrarFeedback(`Incorrecto. La respuesta era: ${this.palabraActual}`, 'incorrecto');
        this.actualizarInterfaz();
        
        if (this.vidas <= 0) {
            setTimeout(() => {
                this.gameOver();
            }, 2000);
        } else {
            setTimeout(() => {
                this.nuevaPalabra();
            }, 2000);
        }
    }
    
    calcularPuntos() {
        let puntos = this.nivel * 10; // Puntos base por nivel
        
        // Bonus por no usar pista
        if (!this.pistaUsada) {
            puntos += 5;
        }
        
        // Bonus por longitud de palabra
        puntos += this.palabraActual.length;
        
        return puntos;
    }
    
    mostrarPista() {
        if (!this.pistaUsada) {
            this.puntuacion = Math.max(0, this.puntuacion - 10);
            this.pistaUsada = true;
            
            document.getElementById('pista').textContent = this.pistaActual;
            document.getElementById('pista').classList.remove('hidden');
            document.getElementById('btn-pista').style.display = 'none';
            
            this.actualizarInterfaz();
        }
    }
    
    saltarPalabra() {
        this.puntuacion = Math.max(0, this.puntuacion - 5);
        this.mostrarFeedback(`Palabra saltada. Era: ${this.palabraActual}`, 'incorrecto');
        this.actualizarInterfaz();

        setTimeout(() => {
            this.nuevaPalabra();
        }, 1500);
    }

    completarNivel() {
        const bonusNivel = this.nivel * 20;
        this.puntuacion += bonusNivel;

        // Mostrar modal de nivel completado
        document.getElementById('nivel-completado').textContent = this.nivel;
        document.getElementById('puntos-nivel').textContent = this.puntuacion;
        document.getElementById('bonus-nivel').textContent = bonusNivel;
        document.getElementById('modal-nivel').classList.remove('hidden');
    }

    siguienteNivel() {
        this.nivel++;
        this.palabrasCompletadas = 0;
        this.vidas = Math.min(3, this.vidas + 1); // Recuperar una vida

        document.getElementById('modal-nivel').classList.add('hidden');

        if (this.palabras[this.nivel]) {
            this.actualizarInterfaz();
            this.nuevaPalabra();
        } else {
            this.mostrarVictoria();
        }
    }

    gameOver() {
        document.getElementById('puntuacion-final').textContent = this.puntuacion;
        document.getElementById('nivel-final').textContent = this.nivel;
        document.getElementById('modal-game-over').classList.remove('hidden');
    }

    mostrarVictoria() {
        document.getElementById('puntuacion-victoria').textContent = this.puntuacion;
        document.getElementById('modal-victoria').classList.remove('hidden');
    }

    nuevoJuego() {
        this.nivel = 1;
        this.puntuacion = 0;
        this.vidas = 3;
        this.palabrasCompletadas = 0;
        this.pistaUsada = false;

        // Ocultar todos los modales
        document.getElementById('modal-nivel').classList.add('hidden');
        document.getElementById('modal-game-over').classList.add('hidden');
        document.getElementById('modal-victoria').classList.add('hidden');

        this.actualizarInterfaz();
        this.nuevaPalabra();
    }

    actualizarInterfaz() {
        // Actualizar estadísticas
        document.getElementById('nivel').textContent = this.nivel;
        document.getElementById('puntuacion').textContent = this.puntuacion;

        // Actualizar vidas
        const vidasTexto = '❤️'.repeat(this.vidas) + '🖤'.repeat(3 - this.vidas);
        document.getElementById('vidas').textContent = vidasTexto;

        // Actualizar progreso
        const progreso = (this.palabrasCompletadas / this.palabrasPorNivel) * 100;
        document.getElementById('progreso').style.width = `${progreso}%`;
        document.getElementById('palabras-completadas').textContent = this.palabrasCompletadas;
        document.getElementById('palabras-total').textContent = this.palabrasPorNivel;
    }

    mostrarFeedback(mensaje, tipo) {
        const feedback = document.getElementById('feedback');
        feedback.textContent = mensaje;
        feedback.className = `feedback ${tipo}`;
    }

    limpiarFeedback() {
        const feedback = document.getElementById('feedback');
        feedback.textContent = '';
        feedback.className = 'feedback';
    }
}

// Efectos visuales adicionales
class EfectosVisuales {
    static crearParticulas(elemento, color = '#00b894') {
        for (let i = 0; i < 10; i++) {
            const particula = document.createElement('div');
            particula.style.position = 'absolute';
            particula.style.width = '6px';
            particula.style.height = '6px';
            particula.style.backgroundColor = color;
            particula.style.borderRadius = '50%';
            particula.style.pointerEvents = 'none';
            particula.style.zIndex = '1000';

            const rect = elemento.getBoundingClientRect();
            particula.style.left = `${rect.left + rect.width / 2}px`;
            particula.style.top = `${rect.top + rect.height / 2}px`;

            document.body.appendChild(particula);

            // Animar partícula
            const animacion = particula.animate([
                {
                    transform: 'translate(0, 0) scale(1)',
                    opacity: 1
                },
                {
                    transform: `translate(${(Math.random() - 0.5) * 200}px, ${(Math.random() - 0.5) * 200}px) scale(0)`,
                    opacity: 0
                }
            ], {
                duration: 1000,
                easing: 'ease-out'
            });

            animacion.onfinish = () => {
                particula.remove();
            };
        }
    }

    static animarElemento(elemento, animacion = 'bounce') {
        elemento.style.animation = `${animacion} 0.6s ease`;
        setTimeout(() => {
            elemento.style.animation = '';
        }, 600);
    }
}

// Agregar animaciones CSS dinámicamente
const style = document.createElement('style');
style.textContent = `
    @keyframes bounce {
        0%, 20%, 60%, 100% { transform: translateY(0); }
        40% { transform: translateY(-20px); }
        80% { transform: translateY(-10px); }
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);

// Inicializar el juego cuando se carga la página
document.addEventListener('DOMContentLoaded', () => {
    const juego = new JuegoAdivinanza();

    // Agregar efectos visuales a respuestas correctas
    const originalRespuestaCorrecta = juego.respuestaCorrecta.bind(juego);
    juego.respuestaCorrecta = function() {
        EfectosVisuales.crearParticulas(document.getElementById('palabra-desordenada'), '#00b894');
        EfectosVisuales.animarElemento(document.getElementById('palabra-desordenada'), 'bounce');
        originalRespuestaCorrecta();
    };

    // Agregar efectos visuales a respuestas incorrectas
    const originalRespuestaIncorrecta = juego.respuestaIncorrecta.bind(juego);
    juego.respuestaIncorrecta = function() {
        EfectosVisuales.animarElemento(document.getElementById('palabra-desordenada'), 'shake');
        originalRespuestaIncorrecta();
    };
});
